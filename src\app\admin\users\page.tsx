import { requireSystemAdmin } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { UserManagementPage } from '@/components/admin/UserManagementPage'

export default async function AdminUsersPage() {
  const profile = await requireSystemAdmin()

  return (
    <DashboardLayout user={{
      name: profile.full_name,
      email: profile.email,
      role: profile.role // Use actual role from database
    }}>
      <UserManagementPage />
    </DashboardLayout>
  )
}
