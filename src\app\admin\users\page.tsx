import { requireSystemAdmin } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { UserManagementPage } from '@/components/admin/UserManagementPage'

export default async function AdminUsersPage() {
  const profile = await requireSystemAdmin()

  return (
    <DashboardLayout user={{
      name: profile.full_name,
      email: profile.email,
      role: 'system_admin'
    }}>
      <UserManagementPage />
    </DashboardLayout>
  )
}
