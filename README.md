# سحابة المدينة (Sahabat Al-Madinah)

نظام إدارة العمل المخصص باللغة العربية مع دعم RTL كامل.

## المميزات

- 🌐 **دعم كامل للغة العربية**: واجهة مستخدم باللغة العربية مع دعم RTL
- 🎨 **تصميم عصري**: استخدام shadcn/ui مع ألوان أبيض وأزرق داكن
- 🔐 **نظام مصادقة متقدم**: باستخدام Supabase مع إدارة الأدوار
- 👥 **إدارة المستخدمين**: المدير يمكنه إضافة وإدارة المستخدمين
- 📱 **تصميم متجاوب**: يعمل على جميع الأجهزة
- 🔒 **حماية الصفحات**: حماية تلقائية للصفحات حسب الأدوار

## التقنيات المستخدمة

- **Next.js 15** - إطار عمل React
- **TypeScript** - للكتابة الآمنة
- **Tailwind CSS** - للتصميم
- **shadcn/ui** - مكونات واجهة المستخدم
- **Supabase** - قاعدة البيانات والمصادقة
- **IBM Plex Arabic** - الخط العربي

## الإعداد والتشغيل

### 1. تثبيت المتطلبات

```bash
npm install
```

### 2. إعداد متغيرات البيئة

أنشئ ملف `.env.local` وأضف:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 3. إعداد قاعدة البيانات

قم بتشغيل migrations في Supabase بالترتيب:

```sql
-- 1. تشغيل الـ migration الأساسي
-- نسخ محتوى ملف supabase/migrations/001_initial_schema.sql
-- وتشغيله في SQL Editor في Supabase Dashboard

-- 2. إضافة حقل الهاتف
-- نسخ محتوى ملف supabase/migrations/002_add_phone_to_profiles.sql
-- وتشغيله في SQL Editor

-- 3. تحديث نظام الأدوار الهرمي
-- نسخ محتوى ملف supabase/migrations/003_update_role_hierarchy.sql
-- وتشغيله في SQL Editor
```

### 4. إنشاء أول مدير نظام

بعد تشغيل جميع migrations، قم بإنشاء مستخدم في Supabase Auth، ثم قم بتحديث دوره:

```sql
-- تحديث المستخدم ليصبح مدير نظام
UPDATE profiles
SET role = 'system_admin', role_level = 1
WHERE email = '<EMAIL>';
```

**ملاحظة:** بعد تشغيل migration الأدوار الهرمي، سيتم تحديث المستخدمين الحاليين تلقائياً:
- المدراء السابقون → مدير نظام (system_admin)
- المستخدمون العاديون → موظف مبيعات (sales_employee)

### 5. تشغيل المشروع

```bash
npm run dev
```

افتح [http://localhost:3000](http://localhost:3000) في المتصفح.

## هيكل المشروع

```
src/
├── app/                    # صفحات Next.js
│   ├── admin/             # صفحات المدير
│   ├── dashboard/         # صفحات المستخدم العادي
│   ├── login/             # صفحة تسجيل الدخول
│   └── unauthorized/      # صفحة عدم التصريح
├── components/            # المكونات
│   ├── admin/            # مكونات المدير
│   ├── providers/        # مزودي السياق
│   └── ui/               # مكونات shadcn/ui
├── hooks/                # React Hooks
├── lib/                  # المكتبات والأدوات
│   └── supabase/        # إعداد Supabase
└── middleware.ts         # حماية الصفحات
```

## الأدوار والصلاحيات

النظام يدعم هيكل هرمي من 4 أدوار مرتبة حسب الأهمية والصلاحيات:

### 1. مدير النظام (System Admin) - المستوى الأول
- **الصلاحيات الكاملة**: إدارة جميع أجزاء النظام
- **إدارة المستخدمين**: إضافة، تعديل، وحذف جميع المستخدمين
- **تعيين الأدوار**: يمكنه تعيين أي دور للمستخدمين الآخرين
- **الوصول الإداري**: الوصول الكامل للوحة الإدارة
- **التقارير الشاملة**: عرض جميع التقارير والإحصائيات
- **إعدادات النظام**: التحكم في إعدادات النظام والأمان

### 2. مدير منطقة (Area Manager) - المستوى الثاني
- **إدارة الفرق**: الإشراف على فرق العمل في المنطقة
- **تقارير المنطقة**: عرض تقارير الأداء للمنطقة
- **إدارة المشاريع**: إنشاء وإدارة مشاريع المنطقة
- **الإشراف على مديري الفرق**: متابعة أداء مديري الفرق
- **تعيين أدوار محدودة**: يمكنه تعيين أدوار مدير فريق وموظف مبيعات

### 3. مدير فريق (Team Manager) - المستوى الثالث
- **إدارة الفريق**: الإشراف على أعضاء الفريق
- **تقارير الفريق**: عرض تقارير أداء الفريق
- **توزيع المهام**: تعيين المهام لأعضاء الفريق
- **متابعة الأداء**: تقييم ومتابعة أداء موظفي المبيعات
- **إدارة المشاريع**: إدارة مشاريع الفريق

### 4. موظف مبيعات (Sales Employee) - المستوى الرابع
- **المهام الشخصية**: عرض وإدارة المهام المعينة له
- **المشاريع الشخصية**: الوصول للمشاريع المشارك فيها
- **التقويم الشخصي**: إدارة المواعيد والأحداث
- **الملف الشخصي**: تحديث البيانات الشخصية
- **التقارير الشخصية**: عرض تقارير الأداء الشخصي

## هيكل الصلاحيات

```
مدير النظام (1)
    ↓ يدير
مدير منطقة (2)
    ↓ يدير
مدير فريق (3)
    ↓ يدير
موظف مبيعات (4)
```

**قواعد الصلاحيات:**
- كل مستوى يمكنه إدارة المستويات الأدنى منه
- لا يمكن لأي مستوى إدارة المستويات الأعلى منه
- مدير النظام هو الوحيد الذي يمكنه إنشاء مديري نظام آخرين

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التغييرات مع الالتزام بالمعايير
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
