'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { UserAvatar } from '@/components/ui/user-avatar'
import { Separator } from '@/components/ui/separator'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { ArrowLeft, Edit, Trash2, Mail, Phone, Calendar, User, Shield, Save, X } from 'lucide-react'
import { UserRole, RoleLevel } from '@/lib/supabase'
import { getRoleArabicName, getRoleBadgeVariant, getAssignableRoles } from '@/lib/roles'
import { useAuth } from '@/hooks/useAuth'

type Profile = {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  role: UserRole
  role_level: RoleLevel
  created_at: string
  updated_at: string
}

interface UserProfilePageProps {
  userId: string
}

export function UserProfilePage({ userId }: UserProfilePageProps) {
  const router = useRouter()
  const { profile: currentUserProfile } = useAuth()
  const [user, setUser] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [isEditing, setIsEditing] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  const [editForm, setEditForm] = useState({
    full_name: '',
    phone: '',
    role: 'user' as UserRole
  })

  // Get roles that current user can assign (handle old role system)
  const assignableRoles = currentUserProfile ?
    (currentUserProfile.role === 'admin' ? ['admin', 'user'] : ['user']) :
    []

  const fetchUser = async () => {
    try {
      setLoading(true)
      setError('')

      const response = await fetch(`/api/admin/users/${userId}`, {
        cache: 'no-store'
      })
      const result = await response.json()

      if (!response.ok) {
        if (response.status === 404) {
          setError('المستخدم غير موجود')
        } else {
          setError(result.error || 'خطأ في جلب بيانات المستخدم')
        }
        return
      }

      setUser(result.user)
      setEditForm({
        full_name: result.user.full_name || '',
        phone: result.user.phone || '',
        role: result.user.role
      })
    } catch (err) {
      setError('حدث خطأ في الاتصال')
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateUser = async () => {
    if (!user) return
    
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/admin/update-user', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          full_name: editForm.full_name,
          phone: editForm.phone,
          role: editForm.role
        })
      })

      const result = await response.json()

      if (!response.ok) {
        setError('خطأ في تحديث المستخدم: ' + result.error)
        return
      }

      setSuccess('تم تحديث بيانات المستخدم بنجاح')
      setIsEditing(false)
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000)
      
      // Refresh user data
      await fetchUser()
    } catch (err) {
      setError('حدث خطأ غير متوقع')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteUser = async () => {
    if (!user) return
    
    setIsDeleting(true)
    setError('')

    try {
      const response = await fetch('/api/admin/delete-user', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id })
      })

      const result = await response.json()

      if (!response.ok) {
        setError('خطأ في حذف المستخدم: ' + result.error)
        return
      }

      // Redirect to users list after successful deletion
      router.push('/admin/users')
    } catch (err) {
      setError('حدث خطأ غير متوقع')
    } finally {
      setIsDeleting(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }



  useEffect(() => {
    fetchUser()
  }, [userId])

  if (loading && !user) {
    return (
      <div className="space-y-6" dir="rtl">
        <div className="flex items-center gap-4">
          <div className="h-8 w-8 bg-muted rounded animate-pulse"></div>
          <div className="h-8 bg-muted rounded animate-pulse w-48"></div>
        </div>
        <div className="h-96 bg-muted rounded animate-pulse"></div>
      </div>
    )
  }

  if (error && !user) {
    return (
      <div className="space-y-6" dir="rtl">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 ml-2" />
            العودة
          </Button>
          <h2 className="text-3xl font-bold">ملف المستخدم</h2>
        </div>
        <div className="text-center text-destructive bg-destructive/10 p-8 rounded-lg">
          <p className="text-lg font-medium mb-4">{error}</p>
          <div className="flex gap-2 justify-center">
            <Button onClick={fetchUser}>
              إعادة المحاولة
            </Button>
            <Button variant="outline" onClick={() => router.back()}>
              العودة للقائمة
            </Button>
          </div>
        </div>
      </div>
    )
  }

  if (!user) return null

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 ml-2" />
            العودة
          </Button>
          <div>
            <h2 className="text-3xl font-bold">ملف المستخدم</h2>
            <p className="text-muted-foreground mt-2">
              عرض وتعديل بيانات المستخدم
            </p>
          </div>
        </div>
        
        <div className="flex gap-2">
          {!isEditing && (
            <Button onClick={() => setIsEditing(true)}>
              <Edit className="h-4 w-4 ml-2" />
              تعديل
            </Button>
          )}
          
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="h-4 w-4 ml-2" />
                حذف
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent className="text-right" dir="rtl">
              <AlertDialogHeader className="text-right">
                <AlertDialogTitle className="text-right">تأكيد الحذف</AlertDialogTitle>
                <AlertDialogDescription className="text-right">
                  هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع بيانات المستخدم نهائياً.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter className="flex-row-reverse gap-2">
                <AlertDialogAction 
                  onClick={handleDeleteUser}
                  disabled={isDeleting}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  {isDeleting ? 'جاري الحذف...' : 'حذف نهائياً'}
                </AlertDialogAction>
                <AlertDialogCancel>إلغاء</AlertDialogCancel>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {/* Success/Error Messages */}
      {(success || error) && (
        <div dir="rtl">
          {error && (
            <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md mb-2 text-right">
              {error}
            </div>
          )}
          {success && (
            <div className="text-sm text-green-800 bg-green-100 p-3 rounded-md text-right">
              {success}
            </div>
          )}
        </div>
      )}

      {/* User Profile Card */}
      <Card>
        <CardHeader>
          <div className="flex items-start gap-6">
            <UserAvatar
              src=""
              alt={user.full_name || 'مستخدم'}
              name={user.full_name}
              size="xl"
            />
            
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-3">
                <h3 className="text-2xl font-bold">
                  {user.full_name || 'غير محدد'}
                </h3>
                <Badge variant={getRoleBadgeVariant(user.role)}>
                  {getRoleArabicName(user.role)}
                </Badge>
              </div>
              
              <div className="flex items-center gap-2 text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span>{user.email}</span>
              </div>
              
              {user.phone && (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Phone className="h-4 w-4" />
                  <span>{user.phone}</span>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* User Details */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              المعلومات الأساسية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {isEditing ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="edit_full_name" className="text-right block">الاسم الكامل</Label>
                  <Input
                    id="edit_full_name"
                    value={editForm.full_name}
                    onChange={(e) => setEditForm({ ...editForm, full_name: e.target.value })}
                    placeholder="أدخل الاسم الكامل"
                    className="text-right"
                    dir="rtl"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit_phone" className="text-right block">رقم الهاتف</Label>
                  <Input
                    id="edit_phone"
                    type="tel"
                    value={editForm.phone}
                    onChange={(e) => setEditForm({ ...editForm, phone: e.target.value })}
                    placeholder="أدخل رقم الهاتف"
                    className="text-right"
                    dir="rtl"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit_role" className="text-right block">الدور</Label>
                  <Select value={editForm.role} onValueChange={(value: UserRole) => setEditForm({ ...editForm, role: value })} dir="rtl">
                    <SelectTrigger className="text-right" dir="rtl">
                      <SelectValue placeholder="اختر الدور" />
                    </SelectTrigger>
                    <SelectContent className="text-right" dir="rtl">
                      {assignableRoles.map((role) => (
                        <SelectItem key={role} value={role} className="text-right">
                          {role === 'admin' ? 'مدير النظام' : role === 'user' ? 'مستخدم عادي' : getRoleArabicName(role)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex justify-start gap-2 pt-4" dir="rtl">
                  <Button onClick={handleUpdateUser} disabled={loading}>
                    <Save className="h-4 w-4 ml-2" />
                    {loading ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                  </Button>
                  <Button variant="outline" onClick={() => setIsEditing(false)}>
                    <X className="h-4 w-4 ml-2" />
                    إلغاء
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <Label className="text-sm text-muted-foreground">الاسم الكامل</Label>
                  <p className="text-sm font-medium">{user.full_name || 'غير محدد'}</p>
                </div>

                <Separator />

                <div>
                  <Label className="text-sm text-muted-foreground">البريد الإلكتروني</Label>
                  <p className="text-sm font-medium">{user.email}</p>
                </div>

                <Separator />

                <div>
                  <Label className="text-sm text-muted-foreground">رقم الهاتف</Label>
                  <p className="text-sm font-medium">{user.phone || 'غير محدد'}</p>
                </div>

                <Separator />

                <div>
                  <Label className="text-sm text-muted-foreground">الدور</Label>
                  <div className="flex items-center gap-2 mt-1">
                    {user.role === 'admin' ? (
                      <Shield className="h-4 w-4 text-blue-600" />
                    ) : (
                      <User className="h-4 w-4 text-gray-600" />
                    )}
                    <span className="text-sm font-medium">
                      {user.role === 'admin' ? 'مدير' : 'مستخدم عادي'}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Account Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              معلومات الحساب
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm text-muted-foreground">تاريخ إنشاء الحساب</Label>
              <p className="text-sm font-medium">{formatDate(user.created_at)}</p>
            </div>

            <Separator />

            <div>
              <Label className="text-sm text-muted-foreground">آخر تحديث</Label>
              <p className="text-sm font-medium">{formatDate(user.updated_at)}</p>
            </div>

            <Separator />

            <div>
              <Label className="text-sm text-muted-foreground">معرف المستخدم</Label>
              <p className="text-xs font-mono bg-muted p-2 rounded">{user.id}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
