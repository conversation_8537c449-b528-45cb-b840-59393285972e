import { UserR<PERSON>, RoleLevel, ROL<PERSON>_HIERARCHY, RoleInfo } from './supabase'

/**
 * Role hierarchy utility functions for permission management
 */

/**
 * Check if a role has higher or equal privileges than another role
 */
export function hasHigherOrEqualRole(userRole: UserRole, requiredRole: UserRole): boolean {
  const userLevel = ROLE_HIERARCHY[userRole].level
  const requiredLevel = ROLE_HIERARCHY[requiredRole].level
  return userLevel <= requiredLevel // Lower level number = higher privileges
}

/**
 * Check if a role level has higher or equal privileges than required level
 */
export function hasHigherOrEqualRoleLevel(userLevel: RoleLevel, requiredLevel: RoleLevel): boolean {
  return userLevel <= requiredLevel // Lower level number = higher privileges
}

/**
 * Get role information by role name
 */
export function getRoleInfo(role: UserRole): RoleInfo {
  return ROLE_HIERARCHY[role]
}

/**
 * Get Arabic name for a role
 */
export function getRoleArabicName(role: UserRole): string {
  return ROLE_HIERARCHY[role].arabicName
}

/**
 * Get all roles that are at or below a certain level
 */
export function getRolesAtOrBelowLevel(level: RoleLevel): UserRole[] {
  return Object.keys(ROLE_HIERARCHY).filter(
    role => ROLE_HIERARCHY[role as UserRole].level >= level
  ) as UserRole[]
}

/**
 * Get all roles that are above a certain level
 */
export function getRolesAboveLevel(level: RoleLevel): UserRole[] {
  return Object.keys(ROLE_HIERARCHY).filter(
    role => ROLE_HIERARCHY[role as UserRole].level < level
  ) as UserRole[]
}

/**
 * Check if user can manage another user based on role hierarchy
 */
export function canManageUser(managerRole: UserRole, targetRole: UserRole): boolean {
  const managerLevel = ROLE_HIERARCHY[managerRole].level
  const targetLevel = ROLE_HIERARCHY[targetRole].level
  
  // Can manage users at same level or below (higher level number)
  return managerLevel <= targetLevel
}

/**
 * Check if user can view another user based on role hierarchy
 */
export function canViewUser(viewerRole: UserRole, targetRole: UserRole): boolean {
  // Same logic as canManageUser for now, but can be customized later
  return canManageUser(viewerRole, targetRole)
}

/**
 * Get the highest role (lowest level number) from an array of roles
 */
export function getHighestRole(roles: UserRole[]): UserRole | null {
  if (roles.length === 0) return null
  
  return roles.reduce((highest, current) => {
    return ROLE_HIERARCHY[current].level < ROLE_HIERARCHY[highest].level ? current : highest
  })
}

/**
 * Get the lowest role (highest level number) from an array of roles
 */
export function getLowestRole(roles: UserRole[]): UserRole | null {
  if (roles.length === 0) return null
  
  return roles.reduce((lowest, current) => {
    return ROLE_HIERARCHY[current].level > ROLE_HIERARCHY[lowest].level ? current : lowest
  })
}

/**
 * Check if a role has a specific permission
 */
export function hasPermission(role: UserRole, permission: string): boolean {
  const roleInfo = ROLE_HIERARCHY[role]
  
  // System admin has all permissions
  if (role === 'system_admin') return true
  
  return roleInfo.permissions.includes(permission)
}

/**
 * Get all available roles for selection (useful for dropdowns)
 */
export function getAvailableRoles(): { value: UserRole; label: string; level: RoleLevel }[] {
  return Object.entries(ROLE_HIERARCHY).map(([role, info]) => ({
    value: role as UserRole,
    label: info.arabicName,
    level: info.level
  })).sort((a, b) => a.level - b.level) // Sort by hierarchy level
}

/**
 * Get roles that a user can assign to others
 */
export function getAssignableRoles(userRole: UserRole): UserRole[] {
  const userLevel = ROLE_HIERARCHY[userRole].level
  
  // System admin can assign any role except system_admin to others
  if (userRole === 'system_admin') {
    return ['area_manager', 'team_manager', 'sales_employee']
  }
  
  // Other roles can only assign roles at their level or below
  return Object.keys(ROLE_HIERARCHY).filter(
    role => ROLE_HIERARCHY[role as UserRole].level >= userLevel
  ) as UserRole[]
}

/**
 * Validate if a role assignment is allowed
 */
export function canAssignRole(assignerRole: UserRole, targetRole: UserRole): boolean {
  const assignableRoles = getAssignableRoles(assignerRole)
  return assignableRoles.includes(targetRole)
}

/**
 * Get role badge variant for UI components
 */
export function getRoleBadgeVariant(role: UserRole): 'default' | 'secondary' | 'destructive' | 'outline' {
  switch (role) {
    case 'system_admin':
      return 'destructive' // Red for highest privilege
    case 'area_manager':
      return 'default' // Blue for high privilege
    case 'team_manager':
      return 'secondary' // Gray for medium privilege
    case 'sales_employee':
      return 'outline' // Outline for lowest privilege
    default:
      return 'outline'
  }
}

/**
 * Check if user is system admin
 */
export function isSystemAdmin(role: UserRole): boolean {
  return role === 'system_admin'
}

/**
 * Check if user is area manager or higher
 */
export function isAreaManagerOrHigher(role: UserRole): boolean {
  return hasHigherOrEqualRole(role, 'area_manager')
}

/**
 * Check if user is team manager or higher
 */
export function isTeamManagerOrHigher(role: UserRole): boolean {
  return hasHigherOrEqualRole(role, 'team_manager')
}

/**
 * Get role hierarchy level as string for display
 */
export function getRoleLevelDisplay(level: RoleLevel): string {
  const levelNames = {
    1: 'المستوى الأول',
    2: 'المستوى الثاني', 
    3: 'المستوى الثالث',
    4: 'المستوى الرابع'
  }
  return levelNames[level]
}

/**
 * Sort users by role hierarchy (highest role first)
 */
export function sortUsersByRole<T extends { role: UserRole }>(users: T[]): T[] {
  return users.sort((a, b) => {
    return ROLE_HIERARCHY[a.role].level - ROLE_HIERARCHY[b.role].level
  })
}
