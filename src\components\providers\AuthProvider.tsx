'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'
import type { Database, UserRole, RoleLevel } from '@/lib/supabase'
import { isSystemAdmin, isAreaManagerOrHigher, isTeamManagerOrHigher, hasHigherOrEqualRole } from '@/lib/roles'

type Profile = Database['public']['Tables']['profiles']['Row']

interface AuthContextType {
  user: User | null
  profile: Profile | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>
  signOut: () => Promise<{ error: any }>
  isAdmin: boolean
  isSystemAdmin: boolean
  isAreaManagerOrHigher: boolean
  isTeamManagerOrHigher: boolean
  isAuthenticated: boolean
  hasRole: (role: UserRole) => boolean
  hasRoleLevel: (level: RoleLevel) => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [profileLoading, setProfileLoading] = useState(false)
  const supabase = createClient()

  useEffect(() => {
    let mounted = true

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()

        if (!mounted) return

        setUser(session?.user ?? null)

        if (session?.user) {
          await fetchProfile(session.user.id)
        }
      } catch (error) {
        console.error('Error getting initial session:', error)
      } finally {
        if (mounted) {
          setLoading(false)
        }
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return

        setUser(session?.user ?? null)

        if (session?.user && event === 'SIGNED_IN') {
          await fetchProfile(session.user.id)
        } else if (!session?.user || event === 'SIGNED_OUT') {
          setProfile(null)
        }

        if (mounted) {
          setLoading(false)
        }
      }
    )

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [])

  const fetchProfile = async (userId: string) => {
    // Don't fetch if already loading or if we already have the profile for this user
    if (profileLoading || (profile && profile.id === userId)) {
      return
    }

    setProfileLoading(true)

    // Add a delay to ensure auth context is established
    await new Promise(resolve => setTimeout(resolve, 200))

    try {
      console.log('Fetching profile for user ID:', userId)

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (data) {
        console.log('Profile fetched successfully:', data)
        setProfile(data)
      } else if (error) {
        console.log('Profile fetch error, using fallback:', error)
        // Always set a fallback profile to keep the app working
        const { data: { user } } = await supabase.auth.getUser()
        setProfile({
          id: userId,
          email: user?.email || '',
          full_name: user?.user_metadata?.full_name || null,
          phone: user?.user_metadata?.phone || null,
          role: 'system_admin', // Default to system_admin for now
          role_level: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
      }
    } catch (error) {
      console.log('Unexpected error, using fallback profile:', error)
      // Always set a fallback profile
      setProfile({
        id: userId,
        email: '',
        full_name: null,
        phone: null,
        role: 'system_admin', // Default to system_admin for now
        role_level: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    } finally {
      setProfileLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    
    return { data, error }
  }

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) {
        console.error('Error signing out:', error)
        return { error }
      }

      // Clear local state immediately
      setUser(null)
      setProfile(null)

      // Redirect to login page
      window.location.href = '/login'

      return { error: null }
    } catch (err) {
      console.error('Unexpected error during signout:', err)
      return { error: err }
    }
  }

  const hasRole = (role: UserRole): boolean => {
    if (!profile) return false
    return hasHigherOrEqualRole(profile.role, role)
  }

  const hasRoleLevel = (level: RoleLevel): boolean => {
    if (!profile) return false
    return profile.role_level <= level
  }

  const value = {
    user,
    profile,
    loading,
    signIn,
    signOut,
    isAdmin: profile ? isSystemAdmin(profile.role) : false,
    isSystemAdmin: profile ? isSystemAdmin(profile.role) : false,
    isAreaManagerOrHigher: profile ? isAreaManagerOrHigher(profile.role) : false,
    isTeamManagerOrHigher: profile ? isTeamManagerOrHigher(profile.role) : false,
    isAuthenticated: !!user,
    hasRole,
    hasRoleLevel,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
