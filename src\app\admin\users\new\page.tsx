import { requireSystemAdmin } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { AddUserForm } from '@/components/admin/AddUserForm'

export default async function AddUserPage() {
  const profile = await requireSystemAdmin()

  return (
    <DashboardLayout user={{
      name: profile.full_name,
      email: profile.email,
      role: profile.role // Use actual role from database
    }}>
      <div className="space-y-6" dir="rtl">
        <div className="text-right">
          <h2 className="text-3xl font-bold">إضافة مستخدم جديد</h2>
          <p className="text-muted-foreground mt-2">
            إضافة مستخدم جديد إلى النظام
          </p>
        </div>

        <AddUserForm
          onUserAdded={() => {
            // This will be handled internally by the AddUserForm component
          }}
        />
      </div>
    </DashboardLayout>
  )
}
