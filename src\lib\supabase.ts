import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Role hierarchy types
export type UserRole = 'system_admin' | 'area_manager' | 'team_manager' | 'sales_employee'

export type RoleLevel = 1 | 2 | 3 | 4

export interface RoleInfo {
  role: UserRole
  level: RoleLevel
  arabicName: string
  permissions: string[]
}

export const ROLE_HIERARCHY: Record<UserRole, RoleInfo> = {
  system_admin: {
    role: 'system_admin',
    level: 1,
    arabicName: 'مدير النظام',
    permissions: ['all']
  },
  area_manager: {
    role: 'area_manager',
    level: 2,
    arabicName: 'مدير منطقة',
    permissions: ['manage_teams', 'view_reports', 'manage_sales_employees']
  },
  team_manager: {
    role: 'team_manager',
    level: 3,
    arabicName: 'مدير فريق',
    permissions: ['manage_sales_employees', 'view_team_reports']
  },
  sales_employee: {
    role: 'sales_employee',
    level: 4,
    arabicName: 'موظف مبيعات',
    permissions: ['view_own_data']
  }
}

export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          phone: string | null
          role: UserRole
          role_level: RoleLevel
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          phone?: string | null
          role?: UserRole
          role_level?: RoleLevel
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          phone?: string | null
          role?: UserRole
          role_level?: RoleLevel
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      has_role_level_or_higher: {
        Args: { required_level: number }
        Returns: boolean
      }
      get_user_role_level: {
        Args: {}
        Returns: number
      }
      get_user_role: {
        Args: {}
        Returns: string
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}
